{"a": 1, "mcpServers": [{"name": "filesystem", "command": "node", "args": ["%APPDATA%\\npm\\node_modules\\@modelcontextprotocol\\server-filesystem\\dist\\index.js"], "env": {}}, {"name": "memory", "command": "node", "args": ["%APPDATA%\\npm\\node_modules\\@modelcontextprotocol\\server-memory\\dist\\index.js"], "env": {}}, {"name": "gpt", "command": "node", "args": ["%APPDATA%\\npm\\node_modules\\@modelcontextprotocol\\server-gpt\\dist\\index.js"], "env": {}}], "b": 2}