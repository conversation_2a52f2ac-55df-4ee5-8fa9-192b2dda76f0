[user]
    # 设置用户名和邮箱
    name = Your Name
    email = <EMAIL>

[core]
    # 设置默认编辑器
    editor = code --wait
    # 自动转换行结束符
    autocrlf = true
    # 设置文件权限
    filemode = false
    # 忽略大小写
    ignorecase = true

[init]
    # 设置默认分支名
    defaultBranch = main

[pull]
    # 设置拉取时的合并策略
    rebase = false

[push]
    # 设置推送策略
    default = simple
    # 自动设置上游分支
    autoSetupRemote = true

[merge]
    # 设置合并工具
    tool = vscode
    # 禁用快进合并的提交信息
    ff = false

[mergetool "vscode"]
    cmd = code --wait $MERGED

[diff]
    # 设置差异工具
    tool = vscode

[difftool "vscode"]
    cmd = code --wait --diff $LOCAL $REMOTE

[alias]
    # 常用别名
    st = status
    co = checkout
    br = branch
    ci = commit
    ca = commit -a
    cm = commit -m
    cam = commit -am
    
    # 日志相关
    lg = log --oneline --graph --decorate
    lga = log --oneline --graph --decorate --all
    last = log -1 HEAD
    
    # 分支相关
    branches = branch -a
    remotes = remote -v
    
    # 撤销相关
    unstage = reset HEAD --
    uncommit = reset --soft HEAD~1
    
    # 清理相关
    cleanup = "!git branch --merged | grep -v '\\*\\|main\\|master\\|develop' | xargs -n 1 git branch -d"
    
    # 推送相关
    pushf = push --force-with-lease
    
    # 查看配置
    config-list = config --list
    
    # 快速添加和提交
    ac = "!git add -A && git commit -m"
    
    # 查看文件历史
    filelog = log -u
    
    # 查看谁修改了文件
    blame-line = blame -L

[color]
    # 启用颜色输出
    ui = auto
    branch = auto
    diff = auto
    status = auto

[color "branch"]
    current = yellow reverse
    local = yellow
    remote = green

[color "diff"]
    meta = yellow bold
    frag = magenta bold
    old = red bold
    new = green bold

[color "status"]
    added = yellow
    changed = green
    untracked = cyan

[credential]
    # Windows凭据管理器
    helper = manager-core

[help]
    # 自动纠正命令
    autocorrect = 1

[rerere]
    # 启用重用记录的解决方案
    enabled = true

[fetch]
    # 自动修剪远程分支
    prune = true

[rebase]
    # 自动应用stash
    autoStash = true
